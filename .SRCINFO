pkgbase = homepage-git
	pkgdesc = A highly customizable homepage (or startpage / application dashboard) with Docker and service API integrations.
	pkgver = 0.9.6.r0.g805f119a2
	pkgrel = 1
	url = https://github.com/gethomepage/homepage
	arch = any
	license = GPL-3.0-only
	makedepends = git
	makedepends = pnpm
	depends = pnpm
	source = git+https://github.com/gethomepage/homepage.git
	source = homepage.service
	source = homepage.sysusers
	source = homepage.tmpfiles
	sha256sums = SKIP
	sha256sums = a839ac5c8b065b4005b517cf07656e30a3fe91270ca5a1dd8aaf71d980441ba6
	sha256sums = b35b3df75248f5dd1298cb1a13921cb40b66998608eae3a8bf0c36562d43d278
	sha256sums = e10cf0af4417326d32acffddd7104ce5e033dfbbf06f7f8f71bc92a1d6c37165

pkgname = homepage-git
